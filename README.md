# SAMD - Learn Without Limits

SAMD is a comprehensive educational platform built with Django that connects students with expert teachers. The platform offers various subscription plans, course management, and integrated payment processing.

## 🚀 Key Features

- **User Authentication**: Secure login and registration system with role-based access
- **Role-Based System**: Separate portals for students and teachers
- **Course Management**: Teachers can create and manage courses with different content types
- **Subscription Plans**: Multiple payment tiers with different access levels
- **Payment Integration**:  payment processing with eSewa

## 💻 Technologies Used

- **Backend**: Django 4.x, Python 3.x
- **Frontend**: HTML5, CSS3, JavaScript
- **Database**: PostgreSQL (recommended for production)
- **Payment Gateway**: eSewa Payment Integration
- **Authentication**: Django Authentication System

## 👥 User Roles

### Students
- Can browse and enroll in courses
- Access course materials based on subscription plan
- Manage their subscription and profile

### Teachers
- Can create and manage courses
- Upload course materials (videos, PDFs)
- Track student enrollments and earnings

## 💳 Payment Plans

SAMD offers three subscription tiers:

1. **Free Plan**
   - Access to some courses

2. **Standard Plan** ($29/month)
   - Access to all basic courses
     
3. **Premium Plan** ($59/month)
   - Access to all courses

## 📸 Screenshots

### Home Page
![image](https://github.com/user-attachments/assets/e8475cc4-ba95-47f8-82ca-d91828e61b71)

### Teacher Portal
![image](https://github.com/user-attachments/assets/5c1fa4c0-b605-40a4-952c-8a5aabcc6fbc)



### Student Dashboard
![image](https://github.com/user-attachments/assets/53f71d62-4cc0-45bb-9ce3-585602516660)



