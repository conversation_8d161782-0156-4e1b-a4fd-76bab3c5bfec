{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - SAMD</title>
    <link rel="stylesheet" href="{% static 'djangoEDUplatform/styles.css' %}">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>SAMD</h2>
                </div>
                <ul class="nav-menu">
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li><a href="{% url 'login' %}">Login</a></li>
                </ul>
            </div> 
        </nav>
    </header>

    <main class="auth-page">
        <div class="container">
            <div class="auth-container">
                <div class="auth-content">
                    <div class="auth-header">
                        <h1>Create Your Account</h1>
                        <p>Join thousands of students and teachers on SAMD</p>
                    </div>

                    <div class="auth-form">
                        <form id="signupForm" method="post">
                        {% csrf_token %}
                            <div class="form-group">
                                <label for="userType">I am a: </label>
                                <div class="user-type-selector">
                                    <div class="user-type-option">
                                        <input type="radio" id="student" name="userType" value="student" checked>
                                        <label for="student">Student</label>
                                    </div>
                                    <div class="user-type-option">
                                        <input type="radio" id="teacher" name="userType" value="teacher">
                                        <label for="teacher">Teacher</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="fullName">Full Name</label>
                                    <input type="text" id="fullName" name="fullName" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" id="password" name="password" required>
                                {% comment %} <div class="password-strength">
                                    <div class="strength-meter">
                                        <div class="strength-bar" id="passwordStrength"></div>
                                    </div>
                                    <span id="passwordFeedback">Password strength</span>
                                </div> {% endcomment %}
                            </div>
                            
                            <div class="form-group">
                                <label for="confirmPassword">Confirm Password</label>
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone_number">Phone Number</label>
                                <input type="text" id="phone_number" name="phone_number" required>
                            </div>

                            <div class="form-group">
                                <label for="esewa_id">eSewa ID</label>
                                <input type="number" id="esewa_id" name="esewa_id" required>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="termsAgree" required>
                                    <label for="termsAgree">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                                </div>
                            </div>
                            
                            {% if error %}
    <div class="form-error" style="color:red;">{{ error }}</div>
{% endif %}
                            
                            <div class="form-error" id="signupError"></div>
                            
                            <button type="submit" class="btn btn-primary btn-large">Create Account</button>
                        </form>
                        
                        <div class="auth-separator">
                            <span>OR</span>
                        </div>
                        
                        <div class="social-login">
                            <button class="btn btn-outline btn-social">
                                <span class="social-icon">G</span>
                                Sign up with Google
                            </button>
                        </div>
                        
                        <div class="auth-redirect">
                            <p>Already have an account? <a href="login.html">Log in</a></p>
                        </div>
                    </div>
                </div>
                
                <div class="auth-image">
                    <img src="{% static 'djangoEDUplatform/images/image.png' %}" alt="Students learning online">
                    <div class="auth-overlay">
                        <div class="auth-quote">
                            <h3>"The beautiful thing about learning is that no one can take it away from you."</h3>
                            <p>— B.B. King</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SAMD</h3>
                    <p>Empowering learners worldwide with quality education and flexible learning options.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="login.html">Login</a></li>
                        <li><a href="signup.html">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>For Users</h4>
                    <ul>
                        <li><a href="student.html">Student Portal</a></li>
                        <li><a href="teacher.html">Teacher Portal</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SAMD. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="{% static 'djangoEDUplatform/script.js' %}"></script>

</body>
</html>