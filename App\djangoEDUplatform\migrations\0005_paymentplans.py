# Generated by Django 5.2.1 on 2025-06-17 04:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('djangoEDUplatform', '0004_alter_userprofile_esewa_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='Paymentplans',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('plan_type', models.CharField(choices=[('free', 'Free Plan'), ('standard', 'Standard Plan'), ('premium', 'Premium Plan')], max_length=20)),
                ('price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('description', models.TextField()),
                ('features', models.TextField(help_text='Enter features separated by newlines')),
                ('is_active', models.<PERSON><PERSON><PERSON><PERSON>ield(default=True)),
            ],
        ),
    ]
