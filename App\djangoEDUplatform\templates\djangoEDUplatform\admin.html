{% load static %}
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <title></title>
        <meta name="description" content="">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link rel="stylesheet" href="">
    </head>
    <body>
    <div id="Teacherko">
    <p>teacher</p>
        {% for teacher in t%}
                {{teacher.full_name}}
        {{student.phone_number}}
        {{student.esewa_number}}
        {{student.role}}

        {{teacher.earning}}
        {% endfor %}
    </div>

<div id="student">
<p> student</p>
        {% for student in s%}
        {{student.full_name}}
        {{student.phone_number}}
        {{student.esewa_number}}
        {{student.role}}
                {% endfor %}


</div>
        <script src="" async defer></script>
    </body>
</html>