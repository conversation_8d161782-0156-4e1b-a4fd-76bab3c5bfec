{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - SAMD</title>
    <link rel="stylesheet" href="{% static 'djangoEDUplatform/styles.css' %}">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>SAMD</h2>
                </div>
                <ul class="nav-menu">
                    <li><a href="{% url 'home' %}">Home</a></li>
                    {% if role == "student" %}
                        <li><a href="{% url 'student' %}">Student Portal</a></li>
                    {% elif role == "teacher" %}
                        <li><a href="{% url 'teacher' %}">Teacher Portal</a></li>
                    {% else %}
                        <li><a href="{% url 'login' %}">Login</a></li>
                    {% endif %}
                </ul>
            </div>
        </nav>
    </header>

    <main class="payment-page">
        <div class="container">
            <div class="payment-container">
                <div class="payment-header">
                    <h1>Complete Your Payment</h1>
                    <p>Choose your plan and start learning today</p>
                </div>

                <div class="payment-content">
                    <div class="plan-selection">
                        <h2>Select Your Plan</h2>
                        <div class="plan-options">
                            {% for plan in payment_plans %}
                            <div class="plan-option" data-plan="{{ plan.plan_type }}">
                                <input type="radio" id="plan{{ plan.id }}" name="plan_id" value="{{ plan.id }}">
                                <label for="plan{{ plan.id }}">
                                    <div class="plan-info">
                                        <h3>{{ plan.name }}</h3>
                                        <div class="plan-price">${{ plan.price }}/month</div>
                                        <ul>
                                            {% for feature in plan.features.splitlines %}
                                                <li>{{ feature }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="payment-form">
                        <h2>Payment Information</h2>
                        <form action="https://rc-epay.esewa.com.np/api/epay/main/v2/form" method="POST">
                            {% csrf_token %}
                            <div class="plan-options">
                                {% for plan in payment_plans %}
                                <div class="plan-option">
                                    <input type="radio" id="plan{{ plan.id }}" name="plan_id" value="{{ plan.id }}">
                                    <label for="plan{{ plan.id }}">
                                        <strong>{{ plan.name }}</strong> - ${{ plan.price }}<br>
                                        <small>{{ plan.description }}</small>
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                            <button type="submit" id="esewaButton">Pay with Esewa</button>
                            {% if form %}
                                {{ form|safe }}
                            {% endif %}

                            <div class="payment-summary">
                                <h3>Order Summary</h3>
                                <div class="summary-row">
                                    <span>Plan:</span>
                                    <span id="selectedPlanName">Free Plan</span>
                                </div>
                                <div class="summary-row">
                                    <span>Monthly Cost:</span>
                                    <span id="selectedPlanPrice">$0.00</span>
                                </div>
                                <div class="summary-row total">
                                    <span>Total:</span>
                                    <span id="totalAmount">$0.00</span>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-large" id="paymentButton">
                                Complete Payment
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Success Modal -->
    <div id="successModal" class="modal">
        <div class="modal-content">
            <div class="success-content">
                <div class="success-icon">✓</div>
                <h2>Payment Successful!</h2>
                <p>Welcome to SAMD! Your account has been activated.</p>
                <div class="success-actions">
                    <a href="student.html" class="btn btn-primary">Go to Dashboard</a>
                    <a href="index.html" class="btn btn-secondary">Back to Home</a>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'djangoEDUplatform/script.js' %}"></script>
    <script>
        // Inject payment plans data from Django
        const paymentPlans = [
            {% for plan in payment_plans %}
                {
                    plan_type: "{{ plan.plan_type }}",
                    name: "{{ plan.name }}",
                    price: {{ plan.price }},
                    description: "{{ plan.description|escapejs }}",
                    is_active: {{ plan.is_active|lower }}
                }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ];
        
        // Make plans available to the main script
        window.paymentPlans = paymentPlans;
    </script>
    <script>
        // Initialize payment page
        document.addEventListener('DOMContentLoaded', function() {
            initializePaymentPage();
        });
    </script>
</body>
</html>
