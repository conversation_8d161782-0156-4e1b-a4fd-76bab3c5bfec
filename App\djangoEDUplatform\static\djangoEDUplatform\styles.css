/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo h2 {
    color: #2563eb;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 30px;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #2563eb;
}

.btn-login {
    background: #2563eb;
    color: white !important;
    padding: 8px 16px;
    border-radius: 6px;
    transition: background 0.3s;
}

.btn-login:hover {
    background: #1d4ed8;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 16px;
}

.btn-primary {
    background: #2563eb;
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.btn-secondary:hover {
    background: #2563eb;
    color: white;
}

.btn-outline {
    background: transparent;
    color: #6b7280;
    border: 2px solid #d1d5db;
}

.btn-outline:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.btn-small {
    padding: 8px 16px;
    font-size: 14px;
}

.btn-large {
    padding: 16px 32px;
    font-size: 18px;
    width: 100%;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 70px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.hero-image img {
    width: 100%;
    border-radius: 12px;
}

/* Sections */
.about, .plans, .stats, .contact {
    padding: 80px 0;
}

.about h2, .plans h2, .contact h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #1f2937;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.feature-card {
    background: white;
    padding: 40px 30px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #1f2937;
}

/* Plans Section */
.plans {
    background: #f8f9fa;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.plan-card {
    background: white;
    border-radius: 12px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    transition: transform 0.3s;
}

.plan-card:hover {
    transform: translateY(-5px);
}

.plan-card.featured {
    border: 3px solid #2563eb;
    transform: scale(1.05);
}

.plan-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #2563eb;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.plan-header h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #1f2937;
}

.plan-price {
    margin-bottom: 30px;
}

.plan-price .currency {
    font-size: 1.2rem;
    vertical-align: top;
}

.plan-price .amount {
    font-size: 3rem;
    font-weight: 700;
    color: #2563eb;
}

.plan-price .period {
    font-size: 1rem;
    color: #6b7280;
}

.plan-features {
    list-style: none;
    margin-bottom: 30px;
}

.plan-features li {
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.plan-features li:last-child {
    border-bottom: none;
}

/* Stats Section */
.stats {
    background: #1f2937;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-item h3 {
    font-size: 3rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 10px;
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-item {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.contact-form {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #2563eb;
}

/* Footer */
footer {
    background: #1f2937;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 20px;
    color: #2563eb;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: #2563eb;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #374151;
    color: #9ca3af;
}

/* Dashboard Styles */
.dashboard {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

.dashboard-header {
    background: white;
    padding: 30px 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 30px;
}

.dashboard-header h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.plan-badge {
    background: #2563eb;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
}

.sidebar {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    height: fit-content;
}

.sidebar h3 {
    margin-bottom: 20px;
    color: #1f2937;
}

.main-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Plan Info */
.current-plan {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.current-plan p {
    margin-bottom: 10px;
}

/* Course Filters */
.course-filters select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
}

/* Courses Grid */
.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.course-card {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s;
    cursor: pointer;
}

.course-card:hover {
    border-color: #2563eb;
    transform: translateY(-2px);
}

.course-card.locked {
    opacity: 0.6;
    cursor: not-allowed;
}

.course-card h3 {
    margin-bottom: 10px;
    color: #1f2937;
}

.course-card p {
    color: #6b7280;
    margin-bottom: 15px;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.course-level {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
}

.course-level.free {
    background: #dcfce7;
    color: #166534;
}

.course-level.standard {
    background: #dbeafe;
    color: #1d4ed8;
}

.course-level.premium {
    background: #fef3c7;
    color: #d97706;
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 30px;
}

.tab-button {
    background: none;
    border: none;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
}

.tab-button.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Upload Form */
.upload-form {
    max-width: 600px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

/* Earnings */
.earnings-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 20px;
}

.earnings-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.earnings-details p {
    margin-bottom: 8px;
    font-size: 14px;
}

.earnings-breakdown {
    max-width: 800px;
}

.earnings-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.summary-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
}

.summary-card h3 {
    margin-bottom: 15px;
    color: #6b7280;
    font-size: 1rem;
}

.summary-card .amount {
    font-size: 2rem;
    font-weight: 700;
    color: #2563eb;
}

/* Payment Page */
.payment-page {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    padding: 40px 0;
}

.payment-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.payment-header {
    text-align: center;
    margin-bottom: 50px;
}

.payment-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #1f2937;
}

.payment-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.plan-selection,
.payment-form {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.plan-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.plan-option {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s;
}

.plan-option:hover {
    border-color: #2563eb;
}

.plan-option input[type="radio"] {
    display: none;
}

.plan-option input[type="radio"]:checked + label {
    background: #eff6ff;
    border-color: #2563eb;
}

.plan-option label {
    display: block;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.plan-info h3 {
    margin-bottom: 10px;
    color: #1f2937;
}

.plan-info .plan-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 15px;
}

.plan-info ul {
    list-style: none;
}

.plan-info ul li {
    padding: 4px 0;
    font-size: 14px;
    color: #6b7280;
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    margin-bottom: 20px;
    color: #1f2937;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.payment-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.summary-row.total {
    font-weight: 700;
    font-size: 1.2rem;
    border-top: 2px solid #e5e7eb;
    padding-top: 10px;
    margin-top: 15px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.modal-large {
    max-width: 90%;
    max-height: 90%;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #6b7280;
}

.close:hover {
    color: #1f2937;
}

.success-content {
    text-align: center;
}

.success-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 20px;
}

.success-content h2 {
    margin-bottom: 15px;
    color: #1f2937;
}

.success-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .payment-content {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .nav-menu {
        flex-direction: column;
        gap: 10px;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .about, .plans, .stats, .contact {
        padding: 60px 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .success-actions {
        flex-direction: column;
    }
}
/* Add these styles to your existing styles.css file */

/* Authentication Pages */
.auth-page {
    margin-top: 70px;
    min-height: calc(100vh - 70px);
    padding: 40px 0;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.auth-content {
    padding: 60px 40px;
}

.auth-header {
    margin-bottom: 40px;
}

.auth-header h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 10px;
}

.auth-header p {
    color: #6b7280;
    font-size: 1.1rem;
}

.auth-form {
    max-width: 450px;
}

.auth-image {
    position: relative;
    height: 100%;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(38, 38, 38, 0.7), rgba(38, 38, 38, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.auth-quote {
    color: white;
    text-align: center;
}

.auth-quote h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 20px;
    line-height: 1.4;
    font-style: italic;
}

.auth-quote p {
    font-size: 1.1rem;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
}

.forgot-password {
    color: #2563eb;
    text-decoration: none;
    font-size: 14px;
}

.forgot-password:hover {
    text-decoration: underline;
}

.auth-separator {
    position: relative;
    text-align: center;
    margin: 30px 0;
}

.auth-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: #e5e7eb;
}

.auth-separator span {
    position: relative;
    background: white;
    padding: 0 15px;
    color: #6b7280;
    font-size: 14px;
}

.social-login {
    margin-bottom: 30px;
}

.btn-social {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.social-icon {
    font-weight: 700;
    font-size: 18px;
}

.auth-redirect {
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

.auth-redirect a {
    color: #2563eb;
    text-decoration: none;
    font-weight: 600;
}

.auth-redirect a:hover {
    text-decoration: underline;
}

.form-error {
    color: #ef4444;
    font-size: 14px;
    margin-bottom: 15px;
    min-height: 20px;
}

/* User Type Selector */
.user-type-selector {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.user-type-option {
    flex: 1;
    position: relative;
}

.user-type-option input[type="radio"] {
    display: none;
}

.user-type-option label {
    display: block;
    padding: 15px;
    text-align: center;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 600;
}

.user-type-option input[type="radio"]:checked + label {
    border-color: #2563eb;
    background: #eff6ff;
    color: #2563eb;
}

/* Password Strength */
.password-strength {
    margin-top: 10px;
}

.strength-meter {
    height: 5px;
    background: #e5e7eb;
    border-radius: 3px;
    margin-bottom: 5px;
}

.strength-bar {
    height: 100%;
    width: 0;
    border-radius: 3px;
    transition: width 0.3s, background 0.3s;
}

#passwordFeedback {
    font-size: 12px;
    color: #6b7280;
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 4px;
}

.checkbox-group label {
    font-size: 14px;
    color: #6b7280;
}

.checkbox-group a {
    color: #2563eb;
    text-decoration: none;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Responsive Design for Auth Pages */
@media (max-width: 992px) {
    .auth-container {
        grid-template-columns: 1fr;
    }
    
    .auth-image {
        display: none;
    }
    
    .auth-content {
        padding: 40px 30px;
    }
    
    .auth-form {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .auth-header h1 {
        font-size: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .user-type-selector {
        flex-direction: column;
        gap: 10px;
    }
}