{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - SAMD</title>
    <link rel="stylesheet" href="{% static 'djangoEDUplatform/styles.css' %}">
</head>
<body>
    <div class="dashboard-header">
        <h1>Teacher Dashboard</h1>
        <div class="user-info">
            <span id="teacherName">Welcome, {{ teacher_name }}!</span>
        </div>
    </div>

    <div class="dashboard-content">
        <div class="sidebar">
            <div class="earnings-info">
                <h3>Your Earnings</h3>
                <div class="earnings-card">
                    <div class="earnings-amount">
                        <span class="currency">$</span>
                        <span id="totalEarnings">{{ total_earnings|floatformat:2 }}</span>
                    </div>
                    <p>Total Earnings</p>
                </div>
                <div class="earnings-details">
                    <p>Students Enrolled: <span id="totalStudents">{{ total_students }}</span></p>
                    <p>Active Courses: <span id="totalCourses">{{ total_courses }}</span></p>
                </div>
            </div>

            <div class="quick-actions">
                <h3>Quick Actions</h3>
                <button class="btn btn-primary btn-small" onclick="showTab('upload')">Upload Course</button>
                <button class="btn btn-secondary btn-small" onclick="showTab('earnings')">View Earnings</button>
            </div>
        </div>

        <div class="main-content">
            <div class="tabs">
                <button class="tab-button active" onclick="showTab('courses')">My Courses</button>
                <button class="tab-button" onclick="showTab('upload')">Upload Course</button>
                <button class="tab-button" onclick="showTab('earnings')">Earnings</button>
            </div>

            <div id="coursesTab" class="tab-content active">
                <h2>My Courses</h2>
                <div class="courses-list" id="teacherCourses">
                    {% if teacher_courses %}
                        {% for course_data in teacher_courses %}
                            <div class="course-item">
                                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 15px;">
                                    <h3>{{ course_data.course.title }}</h3>
                                    <p>{{ course_data.course.description }}</p>
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                                        <div>
                                            <span class="course-level {{ course_data.course.requiredPlan }}">{{ course_data.course.requiredPlan }}</span>
                                            <span style="margin-left: 10px;">{{ course_data.course.enrolledStudents }} students</span>
                                        </div>
                                        <div>
                                            <span style="font-weight: 600;">Earnings: ${{ course_data.earnings|floatformat:2 }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <p>You haven't created any courses yet.</p>
                            <p>Click on the "Upload Course" tab to get started!</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div id="uploadTab" class="tab-content">
                <h2>Upload New Course</h2>
                <form id="courseUploadForm" class="upload-form" method="POST" action="{% url 'upload_course' %}" enctype="multipart/form-data" onsubmit="handleCourseUpload(event)">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="courseTitle">Course Title</label>
                        <input type="text" id="courseTitle" name="title" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="courseDescription">Description</label>
                        <textarea id="courseDescription" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="courseCategory">Category</label>
                        <select id="courseCategory" name="category" required>
                            <option value="">Select Category</option>
                            <option value="programming">Programming</option>
                            <option value="design">Design</option>
                            <option value="business">Business</option>
                            <option value="marketing">Marketing</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="requiredPlan">Required Plan</label>
                        <select id="requiredPlan" name="required_plan" required>
                            <option value="free">Free</option>
                            <option value="standard">Standard</option>
                            <option value="premium">Premium</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="contentType">Content Type</label>
                        <select id="contentType" name="content_type" onchange="toggleContentInput()" required>
                            <option value="">Select Type</option>
                            <option value="pdf">PDF Document</option>
                            <option value="video">Video Link</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="pdfUpload" style="display: none;">
                        <label for="pdfFile">Upload PDF</label>
                        <input type="file" id="pdfFile" name="content_file" accept=".pdf">
                    </div>
                    
                    <div class="form-group" id="videoLink" style="display: none;">
                        <label for="videoUrl">Video URL</label>
                        <input type="url" id="videoUrl" name="video_url" placeholder="https://example.com/video.mp4">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Upload Course</button>
                </form>
            </div>

            <div id="earningsTab" class="tab-content">
                <h2>Earnings Details</h2>
                <div class="earnings-breakdown">
                    <div class="earnings-summary">
                        <div class="summary-card">
                            <h3>This Month</h3>
                            <p class="amount">${{ monthly_earnings|floatformat:2 }}</p>
                        </div>
                        <div class="summary-card">
                            <h3>Total Students</h3>
                            <p class="amount">{{ total_students }}</p>
                        </div>
                        <div class="summary-card">
                            <h3>Commission Rate</h3>
                            <p class="amount">1%</p>
                        </div>
                    </div>
                    
                    <div class="earnings-history">
                        <h3>Recent Enrollments</h3>
                        <div class="earnings-list">
                            {% if teacher_courses %}
                                {% for course_data in teacher_courses %}
                                    {% if course_data.course.enrolledStudents > 0 %}
                                        <div class="earnings-item">
                                            <div class="earnings-item-details">
                                                <p class="earnings-item-title">{{ course_data.course.title }}</p>
                                                <p class="earnings-item-meta">{{ course_data.course.enrolledStudents }} students enrolled</p>
                                            </div>
                                            <div class="earnings-item-amount">
                                                ${{ course_data.earnings|floatformat:2 }}
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                <p>No enrollment data available yet.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{% static 'djangoEDUplatform/script.js' %}"></script>
</body>
</html>
