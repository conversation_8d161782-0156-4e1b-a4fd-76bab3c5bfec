{% load static %}
{% if User.objects.get("role")=="teacher":
%}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Dashboard - SAMD</title>
    <link rel="stylesheet" href="{% static 'djangoEDUplatform/styles.css' %}">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>SAMD</h2>
                </div>
                <ul class="nav-menu">
                    <li><a href="{% url 'home' %}">Home</a></li>
                    <li><a href="{% url 'logout' %}" onclick="logout()">Logout</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main class="dashboard">
        <div class="container">
            <div class="dashboard-header">
                <h1>Student Dashboard</h1>
                <div class="user-info">
                    <span id="studentName">Welcome, {{ user_name }}!</span>
                    <span class="plan-badge" id="currentPlan">{{ user_plan|title }} Plan</span>
                </div>
            </div>

            <div class="dashboard-content">
                <div class="sidebar">
                    <div class="plan-info">
                        <h3>Your Plan</h3>
                        <div class="current-plan" id="planDetails">
                            <p>Current: <strong id="planType">{{ user_plan|title }}</strong></p>
                            <p>Access Level: <span id="accessLevel">
                                {% if user_plan == 'premium' %}
                                    Premium
                                {% elif user_plan == 'standard' %}
                                    Standard
                                {% else %}
                                    Basic
                                {% endif %}
                            </span></p>
                            <a href="{% url 'payment' %}" class="btn btn-primary btn-small">Upgrade Plan</a>
                        </div>
                    </div>
                    
                    <div class="course-filters">
                        <h3>Filter Courses</h3>
                        <select id="categoryFilter">
                            <option value="all">All Categories</option>
                            <option value="programming">Programming</option>
                            <option value="design">Design</option>
                            <option value="business">Business</option>
                            <option value="marketing">Marketing</option>
                        </select>
                        <select id="levelFilter">
                            <option value="all">All Levels</option>
                            <option value="free">Free</option>
                            <option value="standard">Standard</option>
                            <option value="premium">Premium</option>
                        </select>
                    </div>
                </div>

                <div class="main-content">
                    <div class="courses-section">
                        <h2>Available Courses</h2>
                        <div class="courses-grid" id="coursesGrid">
                            <!-- Courses will be loaded here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Course Modal -->
    <div id="courseModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeCourseModal()">&times;</span>
            <div id="courseContent">
                <!-- Course content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- PDF Viewer Modal -->
    <div id="pdfModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close" onclick="closePdfModal()">&times;</span>
            <iframe id="pdfViewer" src="" width="100%" height="600px"></iframe>
        </div>
    </div>

    <!-- Video Player Modal -->
    <div id="videoModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close" onclick="closeVideoModal()">&times;</span>
            <div class="video-container">
                <video id="videoPlayer" controls width="100%">
                    <source src="" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>
    </div>

    <!-- Pass course data to JavaScript -->
    <script>
        const coursesData = {{ courses_json|safe }};
        const userPlan = "{{ user_plan }}";
    </script>

    <script src="{% static 'djangoEDUplatform/script.js' %}"></script>
    <script>
        // Initialize student dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeStudentDashboard();
        });
    </script>
</body>
</html>
